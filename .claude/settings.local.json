{"permissions": {"allow": ["Bash(npx convex dev:*)", "mcp__context7__resolve-library-id", "mcp__brave-search__brave_web_search", "mcp__context7__get-library-docs", "WebFetch(domain:docs.x.ai)", "WebFetch(domain:docs.twitterapi.io)", "mcp__linear__list_users", "mcp__linear__get_team", "mcp__linear__create_project", "mcp__linear__create_issue", "<PERSON><PERSON>(mkdir:*)", "Bash(bun:*)", "mcp__playwright-mcp__browser_navigate", "Bash(lsof:*)", "mcp__playwright-mcp__browser_install", "<PERSON><PERSON>(curl:*)", "mcp__linear__update_issue", "mcp__playwright-mcp__browser_close", "mcp__playwright-mcp__browser_screen_capture", "mcp__playwright-mcp__browser_screen_move_mouse", "mcp__playwright-mcp__browser_press_key", "Bash(npx:*)", "Bash(git add:*)", "Bash(find:*)", "Bash(ls:*)", "WebFetch(domain:labs.convex.dev)", "Bash(CONVEX_DEPLOYMENT=dev:different-leopard-647 npx convex deploy)", "Bash(CONVEX_DEPLOYMENT=dev:different-leopard-647 npx convex dev --once)", "Bash(cp:*)", "Bash(git push:*)", "Bash(git remote add:*)", "Bash(node:*)", "mcp__ide__getDiagnostics", "Bash(convex dev:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(pkill:*)", "mcp__playwright-mcp__browser_tab_new", "mcp__playwright-mcp__browser_wait_for", "WebFetch(domain:docs.convex.dev)", "mcp__magic__21st_magic_component_refiner", "mcp__magic__21st_magic_component_inspiration", "mcp__playwright-mcp__browser_screen_click", "mcp__playwright-mcp__browser_resize", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(true)", "WebFetch(domain:clerk.com)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(claude mcp add -s user playwright-mcp npx -y mcp-remote http://localhost:53360/sse)", "WebFetch(domain:twitterapi.io)", "Bash(turbo check-types:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "<PERSON><PERSON>(diff:*)", "Bash(git commit:*)", "Bash(convex:*)", "mcp__firecrawl-mcp-server__firecrawl_scrape", "mcp__firecrawl-mcp-server__firecrawl_map", "Bash(git pull:*)", "mcp__docker-gateway__browser_navigate", "mcp__convex__status", "mcp__convex__tables", "mcp__convex__functionSpec", "mcp__convex__data", "mcp__docker-gateway__browser_take_screenshot", "mcp__docker-gateway__browser_console_messages", "mcp__exa__web_search_exa", "mcp__docker-gateway__firecrawl_scrape", "Bash(/Users/<USER>/.bun/install/global/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"getOpenRouterClient|generateCompletion\" \"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/packages/backend/convex/responseGeneration.ts\")", "Bash(npm run convex:*)", "Bash(git rm:*)", "Bash(turbo:*)", "mcp__ide__executeCode", "mcp__convex__run", "Bash(git fetch:*)", "Bash(git checkout:*)", "mcp__convex__envList", "<PERSON><PERSON>(cat:*)", "mcp__linear__list_projects", "mcp__linear__list_my_issues", "mcp__linear__get_project", "mcp__linear__update_project"], "deny": []}}