import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import path from "node:path";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [
    TanStackRouterVite(),
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Add polyfills for Node.js modules
      "buffer": "buffer",
      "events": "events",
      "stream": "stream-browserify",
      "util": "util",
      "crypto": "crypto-browserify",
      "@BuddyChipPro/backend": "@BuddyChipAI/backend",
    },
  },
  define: {
    global: 'globalThis',
    // Prevent Ethereum property redefinition
    "process.env": {},
    // Fix for EventEmitter
    "process": JSON.stringify({ env: {} }),
  },
  optimizeDeps: {
    include: [
      'buffer', 
      'events',
      'eventemitter3',
      'convex/react', 
      '@clerk/clerk-react',
      'stream-browserify',
      'util',
      'crypto-browserify'
    ],
    exclude: [
      'jayson',
      'rpc-websockets'
    ],
  },
  server: {
    fs: {
      strict: false,
    },
    hmr: {
      overlay: true,
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          convex: ['convex/react'],
          ui: ['lucide-react', '@radix-ui/react-avatar', '@radix-ui/react-dropdown-menu'],
        },
      },
    },
  },
});
